﻿<script lang="ts" setup name="recordChannel">
import { ref, reactive, onMounted } from 'vue';
import { auth } from '/@/utils/authFunction';
import { ElMessageBox, ElMessage } from 'element-plus';
import { downloadStreamFile } from '/@/utils/download';
import { useRecordChannelApi } from '/@/api/dangAn/recordChannel';
import editDialog from '/@/views/dangAn/recordChannel/component/editDialog.vue';
import printDialog from '/@/views/system/print/component/hiprint/preview.vue';
import ModifyRecord from '/@/components/table/modifyRecord.vue';
import ImportData from '/@/components/table/importData.vue';

const recordChannelApi = useRecordChannelApi();
const printDialogRef = ref();
const editDialogRef = ref();
const importDataRef = ref();
const state = reactive({
	exportLoading: false,
	tableLoading: false,
	stores: {},
	showAdvanceQueryUI: false,
	dropdownData: {} as any,
	selectData: [] as any[],
	tableQueryParams: {} as any, // 查询条件
	tableData: [],
});

// 页面加载时
onMounted(async () => {});

// 查询操作
const handleQuery = async (params: any = {}) => {
	state.tableLoading = true;
	const result = await recordChannelApi.query(Object.assign(state.tableQueryParams)).then((res) => res.data.result);
	state.tableData = result ?? [];
	state.tableLoading = false;
};

// 删除
const delRecordChannel = (row: any) => {
	ElMessageBox.confirm(`确定要删除吗?`, '提示', {
		confirmButtonText: '确定',
		cancelButtonText: '取消',
		type: 'warning',
	})
		.then(async () => {
			await recordChannelApi.delete({ id: row.id });
			handleQuery();
			ElMessage.success('删除成功');
		})
		.catch(() => {});
};

// 批量删除
const batchDelRecordChannel = () => {
	ElMessageBox.confirm(`确定要删除${state.selectData.length}条记录吗?`, '提示', {
		confirmButtonText: '确定',
		cancelButtonText: '取消',
		type: 'warning',
	})
		.then(async () => {
			await recordChannelApi.batchDelete(state.selectData.map((u) => ({ id: u.id }))).then((res) => {
				ElMessage.success(`成功批量删除${res.data.result}条记录`);
				handleQuery();
			});
		})
		.catch(() => {});
};

handleQuery();
</script>
<template>
	<div class="recordChannel-container" v-loading="state.exportLoading">
		<div class="bg-white rounded-md pt-5 px-4 mb-4">
			<el-form :model="state.tableQueryParams" ref="queryForm" :label-width="120" v-if="!state.showAdvanceQueryUI">
				<!-- 简单查询模式 -->
				<el-row :gutter="20" style="margin-bottom: 12px">
					<el-col :span="6">
						<el-form-item label="关键字">
							<el-input v-model="state.tableQueryParams.keyword" clearable placeholder="请输入模糊查询关键字" />
						</el-form-item>
					</el-col>
					<el-col :span="18">
						<el-form-item>
							<div style="display: flex; align-items: center; flex-wrap: nowrap; white-space: nowrap">
								<el-button type="primary" icon="ele-Search" @click="handleQuery" v-auth="'recordChannel:page'" v-reclick="1000"> 查询 </el-button>
								<el-button icon="ele-Refresh" @click="() => (state.tableQueryParams = {})" style="margin-left: 5px"> 重置 </el-button>
								<el-button icon="ele-ZoomIn" @click="() => (state.showAdvanceQueryUI = true)" style="margin-left: 5px"> 高级查询 </el-button>
								<el-button type="danger" style="margin-left: 5px" icon="ele-Delete" @click="batchDelRecordChannel" :disabled="state.selectData.length == 0" v-auth="'recordChannel:batchDelete'">
									删除
								</el-button>
								<el-button type="primary" style="margin-left: 5px" icon="ele-Plus" @click="editDialogRef.openDialog(null, '新增档案柜管理')" v-auth="'recordChannel:add'"> 新增 </el-button>
								<el-button type="warning" style="margin-left: 5px" icon="ele-MostlyCloudy" @click="importDataRef.openDialog()" v-auth="'recordChannel:import'"> 导入 </el-button>
							</div>
						</el-form-item>
					</el-col>
				</el-row>
			</el-form>

			<el-form :model="state.tableQueryParams" ref="queryForm" :label-width="120" v-if="state.showAdvanceQueryUI">
				<!-- 高级查询模式 - 3列布局 -->
				<el-row :gutter="20" style="margin-bottom: 12px">
					<el-col :span="8">
						<el-form-item label="关键字">
							<el-input v-model="state.tableQueryParams.keyword" clearable placeholder="请输入模糊查询关键字" />
						</el-form-item>
					</el-col>
					<el-col :span="8">
						<el-form-item label="名称">
							<el-input v-model="state.tableQueryParams.name" clearable placeholder="请输入名称" />
						</el-form-item>
					</el-col>
					<el-col :span="8">
						<el-form-item label="图片">
							<el-input v-model="state.tableQueryParams.image" clearable placeholder="请输入图片" />
						</el-form-item>
					</el-col>
				</el-row>
				<el-row :gutter="20" style="margin-bottom: 12px">
					<el-col :span="8">
						<el-form-item label="标志">
							<el-input v-model="state.tableQueryParams.flag" clearable placeholder="请输入标志" />
						</el-form-item>
					</el-col>
					<el-col :span="8">
						<el-form-item label="SEO标题">
							<el-input v-model="state.tableQueryParams.seotitle" clearable placeholder="请输入SEO标题" />
						</el-form-item>
					</el-col>
					<el-col :span="8">
						<el-form-item label="关键字">
							<el-input v-model="state.tableQueryParams.keyWords" clearable placeholder="请输入关键字" />
						</el-form-item>
					</el-col>
				</el-row>
				<el-row :gutter="20" style="margin-bottom: 12px">
					<el-col :span="8">
						<el-form-item label="描述">
							<el-input v-model="state.tableQueryParams.description" clearable placeholder="请输入描述" />
						</el-form-item>
					</el-col>
					<el-col :span="8">
						<el-form-item label="自定义名称">
							<el-input v-model="state.tableQueryParams.diyName" clearable placeholder="请输入自定义名称" />
						</el-form-item>
					</el-col>
					<el-col :span="8">
						<el-form-item label="外部链接">
							<el-input v-model="state.tableQueryParams.outLink" clearable placeholder="请输入外部链接" />
						</el-form-item>
					</el-col>
				</el-row>
				<el-row :gutter="20" style="margin-bottom: 12px">
					<el-col :span="8">
						<el-form-item label="链接类型">
							<g-sys-dict v-model="state.tableQueryParams.linkType" code="Link_type" render-as="select" placeholder="请选择链接类型" clearable filterable />
						</el-form-item>
					</el-col>
					<el-col :span="8">
						<el-form-item label="列表数据类型">
							<g-sys-dict v-model="state.tableQueryParams.listType" code="RecordChannelListTypeEnum" render-as="select" placeholder="请选择列表数据类型" clearable filterable />
						</el-form-item>
					</el-col>
					<el-col :span="8">
						<el-form-item label="是否已满">
							<el-select clearable filterable v-model="state.tableQueryParams.isContribute" placeholder="请选择是否已满">
								<el-option value="true" label="是" />
								<el-option value="false" label="否" />
							</el-select>
						</el-form-item>
					</el-col>
				</el-row>
				<el-row :gutter="20" style="margin-bottom: 12px">
					<el-col :span="8">
						<el-form-item label="是否导航显示">
							<el-select clearable filterable v-model="state.tableQueryParams.isNav" placeholder="请选择是否导航显示">
								<el-option value="true" label="是" />
								<el-option value="false" label="否" />
							</el-select>
						</el-form-item>
					</el-col>
					<el-col :span="8">
						<el-form-item label="状态">
							<g-sys-dict v-model="state.tableQueryParams.status" code="StatusEnum" render-as="select" placeholder="请选择状态" clearable filterable />
						</el-form-item>
					</el-col>
					<el-col :span="8">
						<el-form-item label="可存放档案数量">
							<el-input-number v-model="state.tableQueryParams.cabinetMax" clearable placeholder="请输入可存放档案数量" />
						</el-form-item>
					</el-col>
				</el-row>
				<el-row :gutter="20" style="margin-bottom: 12px">
					<el-col :span="8">
						<!-- 空列，用于占位 -->
					</el-col>
					<el-col :span="8">
						<!-- 空列，用于占位 -->
					</el-col>
					<el-col :span="8">
						<el-form-item>
							<div style="display: flex; align-items: center; justify-content: flex-end; width: 100%; flex-wrap: nowrap; white-space: nowrap">
								<el-button type="primary" icon="ele-Search" @click="handleQuery" v-auth="'recordChannel:page'" v-reclick="1000"> 查询 </el-button>
								<el-button icon="ele-Refresh" @click="() => (state.tableQueryParams = {})" style="margin-left: 5px"> 重置 </el-button>
								<el-button icon="ele-ZoomOut" @click="() => (state.showAdvanceQueryUI = false)" style="margin-left: 5px"> 隐藏 </el-button>
							</div>
						</el-form-item>
					</el-col>
				</el-row>
				<el-row :gutter="20">
					<el-col :span="24">
						<el-form-item style="text-align: right">
							<div style="display: flex; align-items: center; justify-content: flex-end; flex-wrap: nowrap; white-space: nowrap">
								<el-button type="danger" icon="ele-Delete" @click="batchDelRecordChannel" :disabled="state.selectData.length == 0" v-auth="'recordChannel:batchDelete'"> 删除 </el-button>
								<el-button type="primary" style="margin-left: 5px" icon="ele-Plus" @click="editDialogRef.openDialog(null, '新增档案柜管理')" v-auth="'recordChannel:add'"> 新增 </el-button>
								<el-button type="warning" style="margin-left: 5px" icon="ele-MostlyCloudy" @click="importDataRef.openDialog()" v-auth="'recordChannel:import'"> 导入 </el-button>
							</div>
						</el-form-item>
					</el-col>
				</el-row>
			</el-form>
		</div>
		<el-card class="full-table" shadow="hover" style="margin-top: 5px">
			<el-table
				:data="state.tableData"
				@selection-change="
					(val: any[]) => {
						state.selectData = val;
					}
				"
				style="width: 100%"
				v-loading="state.tableLoading"
				tooltip-effect="light"
				row-key="id"
				:tree-props="{ children: 'children', hasChildren: 'parentId' }"
				default-expand-all
				border
			>
				<el-table-column prop="name" label="名称" width="200" show-overflow-tooltip />
				<el-table-column prop="diyName" label="自定义名称" show-overflow-tooltip />
				<el-table-column prop="image" label="图片" show-overflow-tooltip />
				<el-table-column prop="flag" label="标志" show-overflow-tooltip />
				<el-table-column prop="seotitle" label="SEO标题" show-overflow-tooltip />
				<el-table-column prop="keyWords" label="关键字" show-overflow-tooltip />
				<el-table-column prop="description" label="描述" show-overflow-tooltip />
				<el-table-column prop="outLink" label="外部链接" show-overflow-tooltip />
				<el-table-column prop="linkType" label="链接类型" show-overflow-tooltip>
					<template #default="scope">
						<g-sys-dict v-model="scope.row.linkType" code="Link_type" />
					</template>
				</el-table-column>
				<el-table-column prop="listType" label="列表数据类型" width="120" show-overflow-tooltip>
					<template #default="scope">
						<span v-if="scope.row.listType != null">
							<g-sys-dict v-model="scope.row.listType" code="RecordChannelListTypeEnum" />
						</span>
					</template>
				</el-table-column>
				<el-table-column prop="cabinetMax" label="可存放档案数量" show-overflow-tooltip />
				<el-table-column prop="items" label="实际档案数量" show-overflow-tooltip />
				<el-table-column prop="isContribute" label="是否已满" show-overflow-tooltip>
					<template #default="scope">
						<el-tag v-if="scope.row.isContribute"> 是 </el-tag>
						<el-tag type="danger" v-else> 否 </el-tag>
					</template>
				</el-table-column>
				<el-table-column prop="isNav" label="是否导航显示" width="100" show-overflow-tooltip>
					<template #default="scope">
						<el-tag v-if="scope.row.isNav == null" type="info"> 未知 </el-tag>
						<el-tag v-else-if="scope.row.isNav"> 是 </el-tag>
						<el-tag type="danger" v-else> 否 </el-tag>
					</template>
				</el-table-column>
				<el-table-column prop="status" label="状态" show-overflow-tooltip>
					<template #default="scope">
						<g-sys-dict v-model="scope.row.status" code="StatusEnum" />
					</template>
				</el-table-column>
				<el-table-column label="修改记录" width="100" align="center" show-overflow-tooltip>
					<template #default="scope">
						<ModifyRecord :data="scope.row" />
					</template>
				</el-table-column>
				<el-table-column label="操作" width="140" align="center" fixed="right" show-overflow-tooltip v-if="auth('recordChannel:update') || auth('recordChannel:delete')">
					<template #default="scope">
						<el-button icon="ele-Edit" size="small" text type="primary" @click="editDialogRef.openDialog(scope.row, '编辑档案柜管理')" v-auth="'recordChannel:update'"> 编辑 </el-button>
						<el-button icon="ele-Delete" size="small" text type="primary" @click="delRecordChannel(scope.row)" v-auth="'recordChannel:delete'"> 删除 </el-button>
					</template>
				</el-table-column>
			</el-table>
			<ImportData ref="importDataRef" :import="recordChannelApi.importData" :download="recordChannelApi.downloadTemplate" v-auth="'recordChannel:import'" @refresh="handleQuery" />
			<printDialog ref="printDialogRef" :title="'打印档案柜管理'" @reloadTable="handleQuery" />
			<editDialog ref="editDialogRef" @reloadTable="handleQuery" />
		</el-card>
	</div>
</template>
<style scoped>
:deep(.el-input),
:deep(.el-select),
:deep(.el-input-number) {
	width: 100%;
}
</style>

﻿<script lang="ts" setup name="recordBasic">
import { ref, reactive, onMounted } from 'vue';
import { auth } from '/@/utils/authFunction';
import { ElMessageBox, ElMessage } from 'element-plus';
import { downloadStreamFile } from '/@/utils/download';
import { useRecordBasicApi } from '/@/api/dangAn/recordBasic';
import editMiniDialog from '/@/views/dangAn/recordBasic/component/editMiniDialog.vue';
import printDialog from '/@/views/system/print/component/hiprint/preview.vue';
import ModifyRecord from '/@/components/table/modifyRecord.vue';
import ImportData from '/@/components/table/importData.vue';

const recordBasicApi = useRecordBasicApi();
const printDialogRef = ref();
const editMiniDialogRef = ref();
const importDataRef = ref();
const state = reactive({
	exportLoading: false,
	tableLoading: false,
	stores: {},
	showAdvanceQueryUI: false,
	dropdownData: {} as any,
	selectData: [] as any[],
	tableQueryParams: {} as any,
	tableParams: {
		page: 1,
		pageSize: 20,
		total: 0,
		field: 'createTime', // 默认的排序字段
		order: 'descending', // 排序方向
		descStr: 'descending', // 降序排序的关键字符
	},
	tableData: [],
});

// 页面加载时
onMounted(async () => {});

// 查询操作
const handleQuery = async (params: any = {}) => {
	state.tableLoading = true;
	state.tableParams = Object.assign(state.tableParams, params);
	const result = await recordBasicApi.page(Object.assign(state.tableQueryParams, state.tableParams)).then((res) => res.data.result);
	state.tableParams.total = result?.total;
	state.tableData = result?.items ?? [];
	state.tableLoading = false;
};

// 列排序
const sortChange = async (column: any) => {
	state.tableParams.field = column.prop;
	state.tableParams.order = column.order;
	await handleQuery();
};

// 删除
const delRecordBasic = (row: any) => {
	ElMessageBox.confirm(`确定要删除吗?`, '提示', {
		confirmButtonText: '确定',
		cancelButtonText: '取消',
		type: 'warning',
	})
		.then(async () => {
			await recordBasicApi.delete({ id: row.id });
			handleQuery();
			ElMessage.success('删除成功');
		})
		.catch(() => {});
};

// 批量删除
const batchDelRecordBasic = () => {
	ElMessageBox.confirm(`确定要删除${state.selectData.length}条记录吗?`, '提示', {
		confirmButtonText: '确定',
		cancelButtonText: '取消',
		type: 'warning',
	})
		.then(async () => {
			await recordBasicApi.batchDelete(state.selectData.map((u) => ({ id: u.id }))).then((res) => {
				ElMessage.success(`成功批量删除${res.data.result}条记录`);
				handleQuery();
			});
		})
		.catch(() => {});
};

// 导出数据
const exportRecordBasicCommand = async (command: string) => {
	try {
		state.exportLoading = true;
		if (command === 'select') {
			const params = Object.assign({}, state.tableQueryParams, state.tableParams, { selectKeyList: state.selectData.map((u) => u.id) });
			await recordBasicApi.exportData(params).then((res) => downloadStreamFile(res));
		} else if (command === 'current') {
			const params = Object.assign({}, state.tableQueryParams, state.tableParams);
			await recordBasicApi.exportData(params).then((res) => downloadStreamFile(res));
		} else if (command === 'all') {
			const params = Object.assign({}, state.tableQueryParams, state.tableParams, { page: 1, pageSize: 99999999 });
			await recordBasicApi.exportData(params).then((res) => downloadStreamFile(res));
		}
	} finally {
		state.exportLoading = false;
	}
};

handleQuery();
</script>
<template>
	<div class="recordBasic-container" v-loading="state.exportLoading">
		<div class="bg-white rounded-md pt-5 px-4 mb-4">
			<el-form :model="state.tableQueryParams" ref="queryForm" :label-width="120" v-if="!state.showAdvanceQueryUI">
				<!-- 简单查询模式 -->
				<el-row :gutter="20" style="margin-bottom: 12px">
					<el-col :span="6">
						<el-form-item label="关键字">
							<el-input v-model="state.tableQueryParams.keyword" clearable placeholder="请输入模糊查询关键字" />
						</el-form-item>
					</el-col>
					<el-col :span="18">
						<el-form-item>
							<div style="display: flex; align-items: center; flex-wrap: nowrap; white-space: nowrap">
								<el-button type="primary" icon="ele-Search" @click="handleQuery" v-auth="'recordBasic:page'" v-reclick="1000"> 查询 </el-button>
								<el-button icon="ele-Refresh" @click="() => (state.tableQueryParams = {})" style="margin-left: 5px"> 重置 </el-button>
								<el-button icon="ele-ZoomIn" @click="() => (state.showAdvanceQueryUI = true)" style="margin-left: 5px"> 高级查询 </el-button>
								<el-button type="danger" style="margin-left: 5px" icon="ele-Delete" @click="batchDelRecordBasic" :disabled="state.selectData.length == 0" v-auth="'recordBasic:batchDelete'">
									删除
								</el-button>
								<el-button type="primary" style="margin-left: 5px" icon="ele-Plus" @click="editMiniDialogRef.openDialog(null, '新增档案基本信息')" v-auth="'recordBasic:add'"> 新增 </el-button>
								<el-dropdown :show-timeout="70" :hide-timeout="50" @command="exportRecordBasicCommand">
									<el-button type="primary" style="margin-left: 5px" icon="ele-FolderOpened" v-reclick="20000" v-auth="'recordBasic:export'"> 导出 </el-button>
									<template #dropdown>
										<el-dropdown-menu>
											<el-dropdown-item command="select" :disabled="state.selectData.length == 0">导出选中</el-dropdown-item>
											<el-dropdown-item command="current">导出本页</el-dropdown-item>
											<el-dropdown-item command="all">导出全部</el-dropdown-item>
										</el-dropdown-menu>
									</template>
								</el-dropdown>
								<el-button type="warning" style="margin-left: 5px" icon="ele-MostlyCloudy" @click="importDataRef.openDialog()" v-auth="'recordBasic:import'"> 导入 </el-button>
							</div>
						</el-form-item>
					</el-col>
				</el-row>
			</el-form>

			<el-form :model="state.tableQueryParams" ref="queryForm" :label-width="120" v-if="state.showAdvanceQueryUI">
				<!-- 高级查询模式 - 3列布局 -->
				<el-row :gutter="20" style="margin-bottom: 12px">
					<el-col :span="8">
						<el-form-item label="关键字">
							<el-input v-model="state.tableQueryParams.keyword" clearable placeholder="请输入模糊查询关键字" />
						</el-form-item>
					</el-col>
					<el-col :span="8">
						<el-form-item label="姓名">
							<el-input v-model="state.tableQueryParams.name" clearable placeholder="请输入姓名" />
						</el-form-item>
					</el-col>
					<el-col :span="8">
						<el-form-item label="曾用名">
							<el-input v-model="state.tableQueryParams.formerName" clearable placeholder="请输入曾用名" />
						</el-form-item>
					</el-col>
				</el-row>
				<el-row :gutter="20" style="margin-bottom: 12px">
					<el-col :span="8">
						<el-form-item label="证件类型">
							<g-sys-dict v-model="state.tableQueryParams.identificationType" code="IdentificationTypeEnum" render-as="select" placeholder="请选择证件类型" clearable filterable />
						</el-form-item>
					</el-col>
					<el-col :span="8">
						<el-form-item label="证件号码">
							<el-input v-model="state.tableQueryParams.identificationNumber" clearable placeholder="请输入证件号码" />
						</el-form-item>
					</el-col>
					<el-col :span="8">
						<el-form-item label="出生日期">
							<el-date-picker
								type="daterange"
								v-model="state.tableQueryParams.birthdayRange"
								value-format="YYYY-MM-DD HH:mm:ss"
								start-placeholder="开始日期"
								end-placeholder="结束日期"
								:default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
							/>
						</el-form-item>
					</el-col>
				</el-row>
				<el-row :gutter="20" style="margin-bottom: 12px">
					<el-col :span="8">
						<el-form-item label="性别">
							<g-sys-dict v-model="state.tableQueryParams.gender" code="GenderEnum" render-as="select" placeholder="请选择性别" clearable filterable />
						</el-form-item>
					</el-col>
					<el-col :span="8">
						<el-form-item label="民族">
							<g-sys-dict v-model="state.tableQueryParams.nationality" code="NationalityEnum" render-as="select" placeholder="请选择民族" clearable filterable />
						</el-form-item>
					</el-col>
					<el-col :span="8">
						<el-form-item label="委托原因">
							<g-sys-dict v-model="state.tableQueryParams.entrustReason" code="Entrust_reason" render-as="select" placeholder="请选择委托原因" clearable filterable />
						</el-form-item>
					</el-col>
				</el-row>
				<el-row :gutter="20" style="margin-bottom: 12px">
					<el-col :span="8">
						<el-form-item label="出生地">
							<el-input v-model="state.tableQueryParams.birthplace" clearable placeholder="请输入出生地" />
						</el-form-item>
					</el-col>
					<el-col :span="8">
						<el-form-item label="籍贯">
							<el-input v-model="state.tableQueryParams.censusRegister" clearable placeholder="请输入籍贯" />
						</el-form-item>
					</el-col>
					<el-col :span="8">
						<el-form-item label="户口编号">
							<el-input v-model="state.tableQueryParams.registerNumber" clearable placeholder="请输入户口编号" />
						</el-form-item>
					</el-col>
				</el-row>
				<el-row :gutter="20" style="margin-bottom: 12px">
					<el-col :span="8">
						<el-form-item label="婚姻状况">
							<g-sys-dict v-model="state.tableQueryParams.maritalStatus" code="MaritalStatusEnum" render-as="select" placeholder="请选择婚姻状况" clearable filterable />
						</el-form-item>
					</el-col>
					<el-col :span="8">
						<el-form-item label="政治面貌">
							<g-sys-dict v-model="state.tableQueryParams.politicsStatus" code="PoliticsStatusEnum" render-as="select" placeholder="请选择政治面貌" clearable filterable />
						</el-form-item>
					</el-col>
					<el-col :span="8">
						<el-form-item label="分支机构">
							<el-input-number v-model="state.tableQueryParams.reasonForProxy" clearable placeholder="请输入分支机构" />
						</el-form-item>
					</el-col>
				</el-row>
				<el-row :gutter="20" style="margin-bottom: 12px">
					<el-col :span="8">
						<el-form-item label="个人身份">
							<g-sys-dict v-model="state.tableQueryParams.userType" code="User_type" render-as="select" placeholder="请选择个人身份" clearable filterable />
						</el-form-item>
					</el-col>
					<el-col :span="8">
						<el-form-item label="委托方式">
							<g-sys-dict v-model="state.tableQueryParams.proxyType" code="Proxy_type" render-as="select" placeholder="请选择委托方式" clearable filterable />
						</el-form-item>
					</el-col>
					<el-col :span="8">
						<el-form-item label="原档案号">
							<el-input v-model="state.tableQueryParams.recordNumberOld" clearable placeholder="请输入原档案号" />
						</el-form-item>
					</el-col>
				</el-row>
				<el-row :gutter="20" style="margin-bottom: 12px">
					<el-col :span="8">
						<el-form-item label="家庭地址">
							<el-input v-model="state.tableQueryParams.homeAddress" clearable placeholder="请输入家庭地址" />
						</el-form-item>
					</el-col>
					<el-col :span="8">
						<el-form-item label="手机">
							<el-input v-model="state.tableQueryParams.mobile" clearable placeholder="请输入手机" />
						</el-form-item>
					</el-col>
					<el-col :span="8">
						<el-form-item label="办公电话">
							<el-input v-model="state.tableQueryParams.officeTel" clearable placeholder="请输入办公电话" />
						</el-form-item>
					</el-col>
				</el-row>
				<el-row :gutter="20" style="margin-bottom: 12px">
					<el-col :span="8">
						<el-form-item label="家庭电话">
							<el-input v-model="state.tableQueryParams.homeTel" clearable placeholder="请输入家庭电话" />
						</el-form-item>
					</el-col>
					<el-col :span="8">
						<el-form-item label="其他号码">
							<el-input v-model="state.tableQueryParams.otherContacts" clearable placeholder="请输入其他号码" />
						</el-form-item>
					</el-col>
					<el-col :span="8">
						<el-form-item label="电子邮箱">
							<el-input v-model="state.tableQueryParams.email" clearable placeholder="请输入电子邮箱" />
						</el-form-item>
					</el-col>
				</el-row>
				<el-row :gutter="20" style="margin-bottom: 12px">
					<el-col :span="8">
						<el-form-item label="家庭邮编">
							<el-input v-model="state.tableQueryParams.homeCode" clearable placeholder="请输入家庭邮编" />
						</el-form-item>
					</el-col>
					<el-col :span="8">
						<el-form-item label="紧急联系人">
							<el-input v-model="state.tableQueryParams.emergencyContact" clearable placeholder="请输入紧急联系人" />
						</el-form-item>
					</el-col>
					<el-col :span="8">
						<el-form-item label="与本人关系">
							<el-input v-model="state.tableQueryParams.relationship" clearable placeholder="请输入与本人关系" />
						</el-form-item>
					</el-col>
				</el-row>
				<el-row :gutter="20" style="margin-bottom: 12px">
					<el-col :span="8">
						<el-form-item label="联系人手机号">
							<el-input v-model="state.tableQueryParams.contactMobile" clearable placeholder="请输入联系人手机号" />
						</el-form-item>
					</el-col>
					<el-col :span="8">
						<el-form-item label="联系人电话">
							<el-input v-model="state.tableQueryParams.contactTel" clearable placeholder="请输入联系人电话" />
						</el-form-item>
					</el-col>
					<el-col :span="8">
						<el-form-item label="邮政编码">
							<el-input v-model="state.tableQueryParams.postalCode" clearable placeholder="请输入邮政编码" />
						</el-form-item>
					</el-col>
				</el-row>
				<el-row :gutter="20" style="margin-bottom: 12px">
					<el-col :span="8">
						<el-form-item label="通信地址">
							<el-input v-model="state.tableQueryParams.communicationAddress" clearable placeholder="请输入通信地址" />
						</el-form-item>
					</el-col>
					<el-col :span="8">
						<el-form-item label="系统识别码">
							<el-input v-model="state.tableQueryParams.cystemIdentificationCode" clearable placeholder="请输入系统识别码" />
						</el-form-item>
					</el-col>
					<el-col :span="8">
						<el-form-item label="参加工作时间">
							<el-input-number v-model="state.tableQueryParams.worktime" clearable placeholder="请输入参加工作时间" />
						</el-form-item>
					</el-col>
				</el-row>
				<el-row :gutter="20" style="margin-bottom: 12px">
					<el-col :span="8">
						<el-form-item label="户籍区划代码">
							<el-input-number v-model="state.tableQueryParams.birthplaceCityCode" clearable placeholder="请输入户籍区划代码" />
						</el-form-item>
					</el-col>
					<el-col :span="8">
						<el-form-item label="健康状况">
							<g-sys-dict v-model="state.tableQueryParams.healthStatus" code="Health_status" render-as="select" placeholder="请选择健康状况" clearable filterable />
						</el-form-item>
					</el-col>
					<el-col :span="8">
						<el-form-item label="语种">
							<el-input v-model="state.tableQueryParams.language" clearable placeholder="请输入语种" />
						</el-form-item>
					</el-col>
				</el-row>
				<el-row :gutter="20" style="margin-bottom: 12px">
					<el-col :span="8">
						<el-form-item label="语种熟练度">
							<g-sys-dict v-model="state.tableQueryParams.languageProficient" code="LanguageProficientEnum" render-as="select" placeholder="请选择语种熟练度" clearable filterable />
						</el-form-item>
					</el-col>
					<el-col :span="8">
						<el-form-item label="宗教信仰">
							<el-input v-model="state.tableQueryParams.belief" clearable placeholder="请输入宗教信仰" />
						</el-form-item>
					</el-col>
					<el-col :span="8">
						<el-form-item label="工作职位(岗位)类型">
							<el-input-number v-model="state.tableQueryParams.companyPositionType" clearable placeholder="请输入工作职位(岗位)类型" />
						</el-form-item>
					</el-col>
				</el-row>
				<el-row :gutter="20" style="margin-bottom: 12px">
					<el-col :span="8">
						<el-form-item label="工作单位ID">
							<el-input v-model="state.tableQueryParams.companyId" clearable placeholder="请输入工作单位ID" />
						</el-form-item>
					</el-col>
					<el-col :span="8">
						<el-form-item label="单位名称">
							<el-input v-model="state.tableQueryParams.companyName" clearable placeholder="请输入单位名称" />
						</el-form-item>
					</el-col>
					<el-col :span="8">
						<el-form-item label="户口所在地址">
							<el-input v-model="state.tableQueryParams.rigisterAddress" clearable placeholder="请输入户口所在地址" />
						</el-form-item>
					</el-col>
				</el-row>
				<el-row :gutter="20" style="margin-bottom: 12px">
					<el-col :span="8">
						<el-form-item label="档案状态">
							<g-sys-dict v-model="state.tableQueryParams.recordStatus" code="RecordStatusEnum" render-as="select" placeholder="请选择档案状态" clearable filterable />
						</el-form-item>
					</el-col>
					<el-col :span="8">
						<el-form-item label="物理地址">
							<el-input v-model="state.tableQueryParams.physicalAddress" clearable placeholder="请输入物理地址" />
						</el-form-item>
					</el-col>
					<el-col :span="8">
						<el-form-item label="所在库">
							<el-input v-model="state.tableQueryParams.channelOneId" clearable placeholder="请输入所在库" />
						</el-form-item>
					</el-col>
				</el-row>
				<el-row :gutter="20" style="margin-bottom: 12px">
					<el-col :span="8">
						<el-form-item label="所在柜">
							<el-input v-model="state.tableQueryParams.channelTwoId" clearable placeholder="请输入所在柜" />
						</el-form-item>
					</el-col>
					<el-col :span="8">
						<el-form-item label="所在层">
							<el-input v-model="state.tableQueryParams.channelThreeId" clearable placeholder="请输入所在层" />
						</el-form-item>
					</el-col>
					<el-col :span="8">
						<el-form-item label="毕业时间">
							<el-date-picker
								type="daterange"
								v-model="state.tableQueryParams.graduateTimeRange"
								value-format="YYYY-MM-DD HH:mm:ss"
								start-placeholder="开始日期"
								end-placeholder="结束日期"
								:default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
							/>
						</el-form-item>
					</el-col>
				</el-row>
				<el-row :gutter="20" style="margin-bottom: 12px">
					<el-col :span="8">
						<el-form-item label="毕业学校">
							<el-input v-model="state.tableQueryParams.graduateSchool" clearable placeholder="请输入毕业学校" />
						</el-form-item>
					</el-col>
					<el-col :span="8">
						<el-form-item label="所学专业">
							<el-input v-model="state.tableQueryParams.graduateMajor" clearable placeholder="请输入所学专业" />
						</el-form-item>
					</el-col>
					<el-col :span="8">
						<el-form-item label="学历">
							<g-sys-dict v-model="state.tableQueryParams.personEdu" code="Person_edu" render-as="select" placeholder="请选择学历" clearable filterable />
						</el-form-item>
					</el-col>
				</el-row>
				<el-row :gutter="20" style="margin-bottom: 12px">
					<el-col :span="8">
						<el-form-item label="学历性质">
							<el-input v-model="state.tableQueryParams.eduNature" clearable placeholder="请输入学历性质" />
						</el-form-item>
					</el-col>
					<el-col :span="8">
						<el-form-item label="入党团时间">
							<el-date-picker
								type="daterange"
								v-model="state.tableQueryParams.politicsTimeRange"
								value-format="YYYY-MM-DD HH:mm:ss"
								start-placeholder="开始日期"
								end-placeholder="结束日期"
								:default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
							/>
						</el-form-item>
					</el-col>
					<el-col :span="8">
						<el-form-item>
							<div style="display: flex; align-items: center; justify-content: flex-end; width: 100%; flex-wrap: nowrap; white-space: nowrap">
								<el-button type="primary" icon="ele-Search" @click="handleQuery" v-auth="'recordBasic:page'" v-reclick="1000"> 查询 </el-button>
								<el-button icon="ele-Refresh" @click="() => (state.tableQueryParams = {})" style="margin-left: 5px"> 重置 </el-button>
								<el-button icon="ele-ZoomOut" @click="() => (state.showAdvanceQueryUI = false)" style="margin-left: 5px"> 隐藏 </el-button>
							</div>
						</el-form-item>
					</el-col>
				</el-row>
				<el-row :gutter="20">
					<el-col :span="24">
						<el-form-item style="text-align: right">
							<div style="display: flex; align-items: center; justify-content: flex-end; flex-wrap: nowrap; white-space: nowrap">
								<el-button type="danger" icon="ele-Delete" @click="batchDelRecordBasic" :disabled="state.selectData.length == 0" v-auth="'recordBasic:batchDelete'"> 删除 </el-button>
								<el-button type="primary" style="margin-left: 5px" icon="ele-Plus" @click="editMiniDialogRef.openDialog(null, '新增档案基本信息')" v-auth="'recordBasic:add'"> 新增 </el-button>
								<el-dropdown :show-timeout="70" :hide-timeout="50" @command="exportRecordBasicCommand">
									<el-button type="primary" style="margin-left: 5px" icon="ele-FolderOpened" v-reclick="20000" v-auth="'recordBasic:export'"> 导出 </el-button>
									<template #dropdown>
										<el-dropdown-menu>
											<el-dropdown-item command="select" :disabled="state.selectData.length == 0">导出选中</el-dropdown-item>
											<el-dropdown-item command="current">导出本页</el-dropdown-item>
											<el-dropdown-item command="all">导出全部</el-dropdown-item>
										</el-dropdown-menu>
									</template>
								</el-dropdown>
								<el-button type="warning" style="margin-left: 5px" icon="ele-MostlyCloudy" @click="importDataRef.openDialog()" v-auth="'recordBasic:import'"> 导入 </el-button>
							</div>
						</el-form-item>
					</el-col>
				</el-row>
			</el-form>
		</div>
		<el-card class="full-table" shadow="hover" style="margin-top: 5px">
			<el-table
				:data="state.tableData"
				@selection-change="
					(val: any[]) => {
						state.selectData = val;
					}
				"
				style="width: 100%"
				v-loading="state.tableLoading"
				tooltip-effect="light"
				row-key="id"
				@sort-change="sortChange"
				border
			>
				<el-table-column type="selection" width="40" align="center" v-if="auth('recordBasic:batchDelete') || auth('recordBasic:export')" />
				<el-table-column type="index" label="序号" width="55" align="center" />
				<el-table-column prop="name" label="姓名" show-overflow-tooltip />
				<el-table-column prop="identificationType" label="证件类型" show-overflow-tooltip>
					<template #default="scope">
						<g-sys-dict v-model="scope.row.identificationType" code="IdentificationTypeEnum" />
					</template>
				</el-table-column>
				<el-table-column prop="identificationNumber" label="证件号码" show-overflow-tooltip />
				<el-table-column prop="gender" label="性别" show-overflow-tooltip>
					<template #default="scope">
						<g-sys-dict v-model="scope.row.gender" code="GenderEnum" />
					</template>
				</el-table-column>
				<el-table-column prop="mobile" label="手机" show-overflow-tooltip />
				<el-table-column prop="recordStatus" label="档案状态" show-overflow-tooltip>
					<template #default="scope">
						<g-sys-dict v-model="scope.row.recordStatus" code="RecordStatusEnum" />
					</template>
				</el-table-column>
				<el-table-column label="修改记录" width="100" align="center" show-overflow-tooltip>
					<template #default="scope">
						<ModifyRecord :data="scope.row" />
					</template>
				</el-table-column>
				<el-table-column label="操作" width="140" align="center" fixed="right" show-overflow-tooltip v-if="auth('recordBasic:update') || auth('recordBasic:delete')">
					<template #default="scope">
						<router-link :to="{ name: 'recordBasicDetail', query: { recordId: scope.row.id } }" style="margin-right: 12px" v-auth="'recordBasic:detail'">
							<el-button icon="ele-Document" size="small" text type="primary" @click="" v-auth="'recordBasic:detail'"> 详细 </el-button>
						</router-link>
						<el-button icon="ele-Delete" size="small" text type="primary" @click="delRecordBasic(scope.row)" v-auth="'recordBasic:delete'"> 删除 </el-button>
					</template>
				</el-table-column>
			</el-table>
			<el-pagination
				v-model:currentPage="state.tableParams.page"
				v-model:page-size="state.tableParams.pageSize"
				@size-change="(val: any) => handleQuery({ pageSize: val })"
				@current-change="(val: any) => handleQuery({ page: val })"
				layout="total, sizes, prev, pager, next, jumper"
				:page-sizes="[10, 20, 50, 100, 200, 500]"
				:total="state.tableParams.total"
				size="small"
				background
			/>
			<ImportData ref="importDataRef" :import="recordBasicApi.importData" :download="recordBasicApi.downloadTemplate" v-auth="'recordBasic:import'" @refresh="handleQuery" />
			<printDialog ref="printDialogRef" :title="'打印档案基本信息'" @reloadTable="handleQuery" />
			<editMiniDialog ref="editMiniDialogRef" @reloadTable="handleQuery" />
		</el-card>
	</div>
</template>
<style scoped>
:deep(.el-input),
:deep(.el-select),
:deep(.el-input-number) {
	width: 100%;
}
</style>
